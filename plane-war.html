<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飞机大战</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(to bottom, #87CEEB, #4682B4);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        #gameContainer {
            position: relative;
            width: 800px;
            height: 600px;
            background: linear-gradient(to bottom, #87CEEB, #4682B4);
            border: 3px solid #333;
            overflow: hidden;
        }
        
        #gameCanvas {
            display: block;
            background: transparent;
        }
        
        #gameInfo {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        #gameOver {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            display: none;
        }
        
        #startButton {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
        }
        
        #startButton:hover {
            background: #45a049;
        }
        
        .controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            color: white;
            font-size: 14px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        <div id="gameInfo">
            <div>得分: <span id="score">0</span></div>
            <div>生命: <span id="lives">3</span></div>
        </div>
        <div id="gameOver">
            <h2>游戏结束</h2>
            <p>最终得分: <span id="finalScore">0</span></p>
            <button id="startButton" onclick="startGame()">重新开始</button>
        </div>
        <div class="controls">
            <div>WASD 或 方向键移动</div>
            <div>空格键射击</div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        // 游戏状态
        let gameRunning = false;
        let score = 0;
        let lives = 3;
        
        // 游戏对象
        let player = {
            x: 375,
            y: 500,
            width: 50,
            height: 50,
            speed: 5,
            color: '#FF6B6B'
        };
        
        let bullets = [];
        let enemies = [];
        let enemyBullets = [];
        
        // 按键状态
        let keys = {};
        
        // 事件监听
        document.addEventListener('keydown', (e) => {
            keys[e.code] = true;
            if (e.code === 'Space') {
                e.preventDefault();
                if (gameRunning) {
                    shoot();
                }
            }
        });
        
        document.addEventListener('keyup', (e) => {
            keys[e.code] = false;
        });
        
        // 射击函数
        function shoot() {
            bullets.push({
                x: player.x + player.width / 2 - 2,
                y: player.y,
                width: 4,
                height: 10,
                speed: 7,
                color: '#FFD93D'
            });
        }
        
        // 创建敌机
        function createEnemy() {
            enemies.push({
                x: Math.random() * (canvas.width - 40),
                y: -40,
                width: 40,
                height: 40,
                speed: 2 + Math.random() * 2,
                color: '#FF4757',
                lastShot: Date.now()
            });
        }
        
        // 敌机射击
        function enemyShoot(enemy) {
            if (Date.now() - enemy.lastShot > 1000 + Math.random() * 2000) {
                enemyBullets.push({
                    x: enemy.x + enemy.width / 2 - 2,
                    y: enemy.y + enemy.height,
                    width: 4,
                    height: 10,
                    speed: 3,
                    color: '#FF6B35'
                });
                enemy.lastShot = Date.now();
            }
        }
        
        // 碰撞检测
        function checkCollision(rect1, rect2) {
            return rect1.x < rect2.x + rect2.width &&
                   rect1.x + rect1.width > rect2.x &&
                   rect1.y < rect2.y + rect2.height &&
                   rect1.y + rect1.height > rect2.y;
        }
        
        // 更新游戏状态
        function update() {
            if (!gameRunning) return;
            
            // 玩家移动
            if (keys['KeyA'] || keys['ArrowLeft']) {
                player.x = Math.max(0, player.x - player.speed);
            }
            if (keys['KeyD'] || keys['ArrowRight']) {
                player.x = Math.min(canvas.width - player.width, player.x + player.speed);
            }
            if (keys['KeyW'] || keys['ArrowUp']) {
                player.y = Math.max(0, player.y - player.speed);
            }
            if (keys['KeyS'] || keys['ArrowDown']) {
                player.y = Math.min(canvas.height - player.height, player.y + player.speed);
            }
            
            // 更新子弹
            bullets = bullets.filter(bullet => {
                bullet.y -= bullet.speed;
                return bullet.y > -bullet.height;
            });
            
            // 更新敌机子弹
            enemyBullets = enemyBullets.filter(bullet => {
                bullet.y += bullet.speed;
                return bullet.y < canvas.height;
            });
            
            // 更新敌机
            enemies = enemies.filter(enemy => {
                enemy.y += enemy.speed;
                enemyShoot(enemy);
                return enemy.y < canvas.height;
            });
            
            // 随机生成敌机
            if (Math.random() < 0.02) {
                createEnemy();
            }
            
            // 碰撞检测 - 子弹击中敌机
            bullets.forEach((bullet, bulletIndex) => {
                enemies.forEach((enemy, enemyIndex) => {
                    if (checkCollision(bullet, enemy)) {
                        bullets.splice(bulletIndex, 1);
                        enemies.splice(enemyIndex, 1);
                        score += 10;
                        updateScore();
                    }
                });
            });
            
            // 碰撞检测 - 敌机子弹击中玩家
            enemyBullets.forEach((bullet, bulletIndex) => {
                if (checkCollision(bullet, player)) {
                    enemyBullets.splice(bulletIndex, 1);
                    lives--;
                    updateLives();
                    if (lives <= 0) {
                        gameOver();
                    }
                }
            });
            
            // 碰撞检测 - 敌机撞击玩家
            enemies.forEach((enemy, enemyIndex) => {
                if (checkCollision(enemy, player)) {
                    enemies.splice(enemyIndex, 1);
                    lives--;
                    updateLives();
                    if (lives <= 0) {
                        gameOver();
                    }
                }
            });
        }
        
        // 绘制游戏
        function draw() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            if (!gameRunning) return;
            
            // 绘制玩家
            ctx.fillStyle = player.color;
            ctx.fillRect(player.x, player.y, player.width, player.height);
            
            // 绘制玩家子弹
            bullets.forEach(bullet => {
                ctx.fillStyle = bullet.color;
                ctx.fillRect(bullet.x, bullet.y, bullet.width, bullet.height);
            });
            
            // 绘制敌机
            enemies.forEach(enemy => {
                ctx.fillStyle = enemy.color;
                ctx.fillRect(enemy.x, enemy.y, enemy.width, enemy.height);
            });
            
            // 绘制敌机子弹
            enemyBullets.forEach(bullet => {
                ctx.fillStyle = bullet.color;
                ctx.fillRect(bullet.x, bullet.y, bullet.width, bullet.height);
            });
        }
        
        // 游戏循环
        function gameLoop() {
            update();
            draw();
            requestAnimationFrame(gameLoop);
        }
        
        // 开始游戏
        function startGame() {
            gameRunning = true;
            score = 0;
            lives = 3;
            bullets = [];
            enemies = [];
            enemyBullets = [];
            player.x = 375;
            player.y = 500;
            
            updateScore();
            updateLives();
            document.getElementById('gameOver').style.display = 'none';
        }
        
        // 游戏结束
        function gameOver() {
            gameRunning = false;
            document.getElementById('finalScore').textContent = score;
            document.getElementById('gameOver').style.display = 'block';
        }
        
        // 更新分数显示
        function updateScore() {
            document.getElementById('score').textContent = score;
        }
        
        // 更新生命显示
        function updateLives() {
            document.getElementById('lives').textContent = lives;
        }
        
        // 初始化游戏
        gameLoop();
        
        // 自动开始游戏
        setTimeout(() => {
            startGame();
        }, 500);
    </script>
</body>
</html>
